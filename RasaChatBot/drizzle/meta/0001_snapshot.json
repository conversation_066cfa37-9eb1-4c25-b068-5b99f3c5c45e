{"id": "72cb08c5-a43a-4c20-9d35-08716faed7e9", "prevId": "652e1829-5ce1-4168-8157-5b051bd29dae", "version": "7", "dialect": "postgresql", "tables": {"public.chats": {"name": "chats", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"chats_user_id_users_id_fk": {"name": "chats_user_id_users_id_fk", "tableFrom": "chats", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.embeddings": {"name": "embeddings", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_input": {"name": "user_input", "type": "text", "primaryKey": false, "notNull": true}, "bot_output": {"name": "bot_output", "type": "text", "primaryKey": false, "notNull": true}, "embedding": {"name": "embedding", "type": "vector(1536)", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {"embeddings_user_id_users_id_fk": {"name": "embeddings_user_id_users_id_fk", "tableFrom": "embeddings", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.messages": {"name": "messages", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "chat_id": {"name": "chat_id", "type": "integer", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"messages_chat_id_chats_id_fk": {"name": "messages_chat_id_chats_id_fk", "tableFrom": "messages", "tableTo": "chats", "columnsFrom": ["chat_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "messages_user_id_users_id_fk": {"name": "messages_user_id_users_id_fk", "tableFrom": "messages", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.relationships": {"name": "relationships", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entity1": {"name": "entity1", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "relationship": {"name": "relationship", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "entity2": {"name": "entity2", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {"idx_relationships_user_id": {"name": "idx_relationships_user_id", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_relationships_entity1": {"name": "idx_relationships_entity1", "columns": [{"expression": "entity1", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "idx_relationships_entity2": {"name": "idx_relationships_entity2", "columns": [{"expression": "entity2", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"relationships_user_id_users_id_fk": {"name": "relationships_user_id_users_id_fk", "tableFrom": "relationships", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"sid": {"name": "sid", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "sess": {"name": "sess", "type": "jsonb", "primaryKey": false, "notNull": true}, "expire": {"name": "expire", "type": "timestamp", "primaryKey": false, "notNull": true}}, "indexes": {"IDX_session_expire": {"name": "IDX_session_expire", "columns": [{"expression": "expire", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_context": {"name": "user_context", "schema": "", "columns": {"id": {"name": "id", "type": "serial", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "context_key": {"name": "context_key", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "context_value": {"name": "context_value", "type": "text", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {"user_context_user_id_users_id_fk": {"name": "user_context_user_id_users_id_fk", "tableFrom": "user_context", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>", "primaryKey": true, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "profile_image_url": {"name": "profile_image_url", "type": "<PERSON><PERSON><PERSON>", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": false, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}